<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NavigationMenu extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'label',
        'icon',
        'url',
        'route',
        'plugin',
        'permissions',
        'parent_id',
        'sort_order',
        'is_active',
        'visible',
        'is_system',
        'target',
        'metadata',
    ];

    protected $casts = [
        'permissions' => 'array',
        'metadata' => 'array',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
    ];

    /**
     * Get the parent menu item
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(NavigationMenu::class, 'parent_id');
    }

    /**
     * Get the child menu items
     */
    public function children(): HasMany
    {
        return $this->hasMany(NavigationMenu::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * Get active child menu items
     */
    public function activeChildren(): HasMany
    {
        return $this->children()->where('is_active', true);
    }

    /**
     * Scope to get only active menu items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only root menu items (no parent)
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get menu items ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if user has permission to see this menu item
     */
    public function userHasPermission($user): bool
    {
        // For debugging: be more permissive
        if (!$this->permissions || empty($this->permissions)) {
            return true; // No permissions required
        }

        if (!$user) {
            return false; // No user, can't check permissions
        }

        // If user is admin or has manage_plugins permission, allow access to everything
        if ($user->hasPermission('manage_plugins')) {
            return true;
        }

        foreach ($this->permissions as $permission) {
            if ($user->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the URL for this menu item
     */
    public function getUrlAttribute($value): ?string
    {
        if ($value) {
            return $value;
        }

        if ($this->route) {
            try {
                return route($this->route);
            } catch (\Exception $e) {
                return null;
            }
        }

        return null;
    }

    /**
     * Check if this menu item is currently active
     */
    public function isActive(): bool
    {
        if (!$this->url) {
            return false;
        }

        $currentUrl = request()->url();
        $currentPath = parse_url($currentUrl, PHP_URL_PATH);
        $menuPath = parse_url($this->url, PHP_URL_PATH);

        return $currentPath === $menuPath || str_starts_with($currentPath, $menuPath . '/');
    }

    /**
     * Get the full navigation tree
     */
    public static function getNavigationTree($user = null): array
    {
        $rootItems = self::root()
            ->ordered()
            ->with(['children' => function ($query) {
                $query->ordered();
            }])
            ->get();

        return $rootItems->filter(function ($item) use ($user) {
            // Check if item should be visible
            $hasPermission = $item->userHasPermission($user);
            $isVisible = $item->visible ?? true; // Default to true if null
            return $hasPermission && $isVisible;
        })->map(function ($item) use ($user) {
            $item->filtered_children = $item->children->filter(function ($child) use ($user) {
                $hasPermission = $child->userHasPermission($user);
                $isVisible = $child->visible ?? true; // Default to true if null
                return $hasPermission && $isVisible;
            });
            return $item;
        })->toArray();
    }

    /**
     * Get the next sort order for a given parent
     */
    public static function getNextSortOrder($parentId = null): int
    {
        return self::where('parent_id', $parentId)->max('sort_order') + 1;
    }
}
