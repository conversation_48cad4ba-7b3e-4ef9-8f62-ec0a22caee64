<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use App\Services\PluginManager;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share the main menu and enabled plugins with all views
        View::composer('*', function ($view) {
            $menuPath = resource_path('data/menu.json');

            if (File::exists($menuPath)) {
                $mainMenu = json_decode(File::get($menuPath), true);
                $view->with('mainMenu', $mainMenu);
            } else {
                $view->with('mainMenu', []);
            }

            // Share enabled plugins with all views
            try {
                $pluginManager = app(PluginManager::class);
                $enabledPlugins = $pluginManager->getEnabledPlugins();
                $view->with('enabledPlugins', $enabledPlugins);
            } catch (\Exception $e) {
                $view->with('enabledPlugins', []);
            }
        });
    }
}
