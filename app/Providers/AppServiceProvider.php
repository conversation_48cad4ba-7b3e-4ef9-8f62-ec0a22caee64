<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use App\Services\PluginManager;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share the navigation menu and enabled plugins with all views
        View::composer('*', function ($view) {
            // Share navigation menu from NavigationMenu model
            try {
                if (class_exists('\App\Models\NavigationMenu')) {
                    $navigationTree = \App\Models\NavigationMenu::getNavigationTree(auth()->user());
                    $view->with('navigationTree', $navigationTree);
                } else {
                    $view->with('navigationTree', []);
                }
            } catch (\Exception $e) {
                $view->with('navigationTree', []);
            }

            // Share enabled plugins with all views
            try {
                $pluginManager = app(PluginManager::class);
                $enabledPlugins = $pluginManager->getEnabledPlugins();
                $view->with('enabledPlugins', $enabledPlugins);
            } catch (\Exception $e) {
                $view->with('enabledPlugins', []);
            }
        });
    }
}
