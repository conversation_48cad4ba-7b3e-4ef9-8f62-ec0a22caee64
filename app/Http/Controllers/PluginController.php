<?php

namespace App\Http\Controllers;

use App\Services\PluginManager;
use App\Traits\DatabaseCompatibility;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class PluginController extends Controller
{
    use DatabaseCompatibility;

    private PluginManager $pluginManager;

    public function __construct(PluginManager $pluginManager)
    {
        $this->pluginManager = $pluginManager;

        // Restrict access to users with manage_plugins permission
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->hasPermission('manage_plugins')) {
                abort(403, 'Access denied. You do not have permission to manage plugins.');
            }

            return $next($request);
        });
    }

    /**
     * Display the plugin manager interface
     */
    public function index(): View
    {
        $plugins = $this->pluginManager->getAllPlugins();
        $stats = $this->pluginManager->getStats();
        $dependencyErrors = $this->pluginManager->validateDependencies();

        return view('plugins.index', compact('plugins', 'stats', 'dependencyErrors'));
    }

    /**
     * Enable a plugin
     */
    public function enable(Request $request, string $name): RedirectResponse
    {
        $result = $this->pluginManager->enablePlugin($name);

        if ($result['success']) {
            return redirect()->route('plugins.index')->with('success', $result['message']);
        }

        return redirect()->route('plugins.index')->with('error', $result['message']);
    }

    /**
     * Disable a plugin
     */
    public function disable(Request $request, string $name): RedirectResponse
    {
        $result = $this->pluginManager->disablePlugin($name);

        if ($result['success']) {
            return redirect()->route('plugins.index')->with('success', $result['message']);
        }

        return redirect()->route('plugins.index')->with('error', $result['message']);
    }

    /**
     * Show plugin details
     */
    public function show(string $name): View
    {
        $plugin = $this->pluginManager->getPlugin($name);

        if (!$plugin) {
            abort(404, 'Plugin not found');
        }

        // Get plugin statistics and data management info
        $stats = $this->getPluginStats($name);
        $hasSeeder = !is_null($this->getPluginSeederClass($name));
        $seederClass = $this->getPluginSeederClass($name);
        $tables = $this->getPluginTables($name);
        $totalDataCount = array_sum($stats);

        return view('plugins.show', compact('plugin', 'stats', 'hasSeeder', 'seederClass', 'tables', 'totalDataCount'));
    }

    /**
     * Refresh plugin list
     */
    public function refresh(): RedirectResponse
    {
        $this->pluginManager->loadPlugins();
        
        return redirect()->route('plugins.index')->with('success', 'Plugin list refreshed successfully.');
    }

    /**
     * Get plugin data as JSON (for API)
     */
    public function api(): \Illuminate\Http\JsonResponse
    {
        $plugins = [];
        foreach ($this->pluginManager->getAllPlugins() as $plugin) {
            $plugins[] = $plugin->toArray();
        }

        return response()->json([
            'plugins' => $plugins,
            'stats' => $this->pluginManager->getStats(),
            'dependency_errors' => $this->pluginManager->validateDependencies(),
        ]);
    }

    /**
     * Validate a specific plugin
     */
    public function validatePlugin(string $name): \Illuminate\Http\JsonResponse
    {
        $validation = $this->pluginManager->validatePlugin($name);
        $canEnable = $this->pluginManager->canEnablePlugin($name);
        $dependencyTree = $this->pluginManager->getDependencyTree($name);
        $affectedPlugins = $this->pluginManager->getAffectedPlugins($name);

        return response()->json([
            'plugin' => $name,
            'validation' => $validation,
            'can_enable' => $canEnable,
            'dependency_tree' => $dependencyTree,
            'affected_plugins' => $affectedPlugins,
        ]);
    }

    /**
     * Uninstall a plugin completely
     */
    public function uninstall(Request $request, string $name): RedirectResponse
    {
        try {
            $plugin = $this->pluginManager->getPlugin($name);

            if (!$plugin) {
                return redirect()->route('plugins.index')->with('error', 'Plugin not found.');
            }

            // Prevent uninstalling system plugins
            if ($plugin->isSystemPlugin()) {
                return redirect()->route('plugins.index')->with('error', 'Cannot uninstall system plugins.');
            }

            // Check if plugin is enabled and disable it first
            if ($plugin->enabled) {
                $disableResult = $this->pluginManager->disablePlugin($name);
                if (!$disableResult['success']) {
                    return redirect()->route('plugins.index')->with('error', 'Failed to disable plugin before uninstall: ' . $disableResult['message']);
                }
            }

            // Remove plugin permissions
            $this->removePluginPermissions($name);

            // Clear plugin data if requested
            if ($request->boolean('clear_data', false)) {
                $tables = $this->getPluginTables($name);
                if (!empty($tables)) {
                    $this->safeDeleteFromTables($tables);
                }
            }

            // Remove plugin files
            $this->removePluginFiles($plugin->path);

            Log::info("Plugin '{$name}' uninstalled successfully", [
                'user' => auth()->user()->email,
                'clear_data' => $request->boolean('clear_data', false),
            ]);

            return redirect()->route('plugins.index')->with('success', "Plugin '{$name}' has been uninstalled successfully.");
        } catch (\Exception $e) {
            Log::error("Failed to uninstall plugin '{$name}': " . $e->getMessage());
            return redirect()->route('plugins.index')->with('error', 'Failed to uninstall plugin: ' . $e->getMessage());
        }
    }

    /**
     * Export a plugin as a downloadable archive
     */
    public function export(string $name)
    {
        try {
            $plugin = $this->pluginManager->getPlugin($name);

            if (!$plugin) {
                return redirect()->route('plugins.index')->with('error', 'Plugin not found.');
            }

            $zipPath = $this->createPluginArchive($plugin);

            Log::info("Plugin '{$name}' exported successfully", [
                'user' => auth()->user()->email,
                'file_size' => filesize($zipPath),
            ]);

            return response()->download($zipPath, "{$name}-plugin-v{$plugin->version}.zip")->deleteFileAfterSend();
        } catch (\Exception $e) {
            Log::error("Failed to export plugin '{$name}': " . $e->getMessage());
            return redirect()->route('plugins.index')->with('error', 'Failed to export plugin: ' . $e->getMessage());
        }
    }

    /**
     * Show the install plugin form
     */
    public function showInstall(): View
    {
        return view('plugins.install');
    }

    /**
     * Install a new plugin from uploaded archive
     */
    public function install(Request $request): RedirectResponse
    {
        $request->validate([
            'plugin_file' => 'required|file|mimes:zip|max:51200', // 50MB max
        ]);

        try {
            $uploadedFile = $request->file('plugin_file');
            $tempPath = $this->extractPluginArchive($uploadedFile);

            // Validate plugin structure
            $configPath = $tempPath . '/config.json';
            if (!file_exists($configPath)) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', 'Invalid plugin archive: config.json not found.');
            }

            $config = json_decode(file_get_contents($configPath), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', 'Invalid plugin archive: config.json is malformed.');
            }

            $pluginName = $config['name'] ?? null;
            if (!$pluginName) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', 'Invalid plugin archive: plugin name not specified.');
            }

            // Check if plugin already exists
            if ($this->pluginManager->pluginExists($pluginName)) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', "Plugin '{$pluginName}' already exists.");
            }

            // Move plugin to plugins directory
            $pluginPath = base_path("plugins/{$pluginName}");
            if (!rename($tempPath, $pluginPath)) {
                $this->cleanupTempDirectory($tempPath);
                return redirect()->route('plugins.index')->with('error', 'Failed to install plugin files.');
            }

            // Reload plugins to include the new one
            $this->pluginManager->loadPlugins();

            Log::info("Plugin '{$pluginName}' installed successfully", [
                'user' => auth()->user()->email,
                'version' => $config['version'] ?? 'unknown',
            ]);

            return redirect()->route('plugins.show', $pluginName)->with('success', "Plugin '{$pluginName}' has been installed successfully.");
        } catch (\Exception $e) {
            Log::error("Failed to install plugin: " . $e->getMessage());
            return redirect()->route('plugins.index')->with('error', 'Failed to install plugin: ' . $e->getMessage());
        }
    }

    /**
     * Check plugin dependencies
     */
    public function checkDependencies(): \Illuminate\Http\JsonResponse
    {
        $errors = $this->pluginManager->validateDependencies();

        return response()->json([
            'has_errors' => !empty($errors),
            'errors' => $errors,
            'total_errors' => count($errors),
        ]);
    }

    /**
     * Seed sample data for a plugin
     */
    public function seedSampleData(Request $request, string $name): JsonResponse
    {
        try {
            $seederClass = $this->getPluginSeederClass($name);

            if (!$seederClass) {
                return response()->json([
                    'success' => false,
                    'message' => 'No sample data seeder found for this plugin.'
                ], 404);
            }

            // Run the sample data seeder
            Artisan::call('db:seed', [
                '--class' => $seederClass,
                '--force' => true
            ]);

            Log::info("Sample data seeded for plugin '{$name}' by user: " . auth()->user()->email);

            return response()->json([
                'success' => true,
                'message' => 'Sample data seeded successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to seed sample data for plugin '{$name}': " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to seed sample data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all data for a plugin
     */
    public function clearAllData(Request $request, string $name): JsonResponse
    {
        try {
            $tables = $this->getPluginTables($name);

            if (empty($tables)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No tables defined for this plugin.'
                ], 404);
            }

            // Clear all plugin data in transaction with database-agnostic foreign key handling
            DB::transaction(function () use ($tables) {
                $this->safeDeleteFromTables($tables);
            });

            Log::info("All data cleared for plugin '{$name}' by user: " . auth()->user()->email);

            return response()->json([
                'success' => true,
                'message' => 'All plugin data cleared successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to clear plugin data for '{$name}': " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to clear data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get plugin statistics
     */
    public function getPluginStats(string $name): array
    {
        switch (strtolower($name)) {
            case 'products':
                return [
                    'products' => DB::table('products')->count(),
                    'pricing_items' => DB::table('product_pricing_items')->count(),
                    'releases' => DB::table('product_releases')->count(),
                    'documents' => DB::table('product_documents')->count(),
                ];

            case 'business':
                return [
                    'businesses' => DB::table('businesses')->count(),
                    'tags' => DB::table('tags')->count(),
                    'business_users' => DB::table('business_users')->count(),
                    'business_products' => DB::table('business_products')->count(),
                ];

            case 'announcements':
                return [
                    'announcements' => DB::table('announcements')->count(),
                    'announcement_reads' => DB::table('announcement_reads')->count(),
                ];

            case 'settings':
                return [
                    'backups' => $this->getBackupCount(),
                    'system_logs' => $this->getLogCount(),
                ];

            default:
                return ['data_count' => 0];
        }
    }

    /**
     * Get plugin seeder class
     */
    private function getPluginSeederClass(string $pluginName): ?string
    {
        $possibleSeeders = [
            "Plugins\\{$pluginName}\\Seeds\\{$pluginName}SampleDataSeeder",
            "Plugins\\{$pluginName}\\Seeds\\{$pluginName}PluginSeeder",
            "Plugins\\{$pluginName}\\Seeds\\SampleDataSeeder",
        ];

        foreach ($possibleSeeders as $seederClass) {
            if (class_exists($seederClass)) {
                return $seederClass;
            }
        }

        return null;
    }

    /**
     * Get plugin tables
     */
    private function getPluginTables(string $pluginName): array
    {
        switch (strtolower($pluginName)) {
            case 'products':
                return [
                    'product_pricing_items',
                    'product_releases',
                    'product_documents',
                    'products'
                ];

            case 'business':
                return [
                    'business_activities',
                    'business_products',
                    'business_tags',
                    'business_users',
                    'businesses',
                    'tags'
                ];

            case 'announcements':
                return [
                    'announcement_reads',
                    'announcements'
                ];

            case 'settings':
                return [
                    // Settings plugin doesn't have database tables to clear
                    // It manages system backups and configuration
                ];

            default:
                return [];
        }
    }

    /**
     * Get backup count for settings plugin
     */
    private function getBackupCount(): int
    {
        try {
            $backupPath = storage_path('app/backups');
            if (!is_dir($backupPath)) {
                return 0;
            }
            return count(glob($backupPath . '/*.zip'));
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get log count for settings plugin
     */
    private function getLogCount(): int
    {
        try {
            $logPath = storage_path('logs');
            if (!is_dir($logPath)) {
                return 0;
            }
            return count(glob($logPath . '/*.log'));
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Remove plugin permissions from database
     */
    private function removePluginPermissions(string $pluginName): void
    {
        try {
            $permissions = \App\Models\Permission::where('plugin', $pluginName)->get();

            foreach ($permissions as $permission) {
                // Detach from all roles first
                $permission->roles()->detach();
                // Delete the permission
                $permission->delete();
            }

            Log::info("Removed {$permissions->count()} permissions for plugin '{$pluginName}'");
        } catch (\Exception $e) {
            Log::error("Failed to remove permissions for plugin '{$pluginName}': " . $e->getMessage());
        }
    }

    /**
     * Remove plugin files and directories
     */
    private function removePluginFiles(string $pluginPath): void
    {
        if (is_dir($pluginPath)) {
            $this->deleteDirectory($pluginPath);
        }
    }

    /**
     * Recursively delete a directory and its contents
     */
    private function deleteDirectory(string $dir): bool
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);

        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;

            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }

        return rmdir($dir);
    }

    /**
     * Create a ZIP archive of a plugin
     */
    private function createPluginArchive(\App\Models\Plugin $plugin): string
    {
        $zip = new \ZipArchive();
        $zipPath = storage_path("app/temp/{$plugin->name}-plugin-v{$plugin->version}.zip");

        // Ensure temp directory exists
        $tempDir = dirname($zipPath);
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        if ($zip->open($zipPath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception('Cannot create ZIP archive');
        }

        $this->addDirectoryToZip($zip, $plugin->path, $plugin->name);
        $zip->close();

        return $zipPath;
    }

    /**
     * Add directory contents to ZIP archive
     */
    private function addDirectoryToZip(\ZipArchive $zip, string $sourcePath, string $baseName): void
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($sourcePath, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            $filePath = $file->getRealPath();
            $relativePath = $baseName . '/' . substr($filePath, strlen($sourcePath) + 1);

            if ($file->isDir()) {
                $zip->addEmptyDir($relativePath);
            } else {
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    /**
     * Extract plugin archive to temporary directory
     */
    private function extractPluginArchive(\Illuminate\Http\UploadedFile $file): string
    {
        $tempDir = storage_path('app/temp/plugin-install-' . uniqid());

        if (!mkdir($tempDir, 0755, true)) {
            throw new \Exception('Cannot create temporary directory');
        }

        $zip = new \ZipArchive();
        if ($zip->open($file->getRealPath()) !== TRUE) {
            $this->cleanupTempDirectory($tempDir);
            throw new \Exception('Cannot open ZIP archive');
        }

        // Extract to temp directory
        $zip->extractTo($tempDir);
        $zip->close();

        // Find the plugin directory (should be the only directory in the archive)
        $contents = scandir($tempDir);
        $pluginDir = null;

        foreach ($contents as $item) {
            if ($item !== '.' && $item !== '..' && is_dir($tempDir . '/' . $item)) {
                $pluginDir = $tempDir . '/' . $item;
                break;
            }
        }

        if (!$pluginDir) {
            $this->cleanupTempDirectory($tempDir);
            throw new \Exception('Invalid plugin archive structure');
        }

        return $pluginDir;
    }

    /**
     * Clean up temporary directory
     */
    private function cleanupTempDirectory(string $dir): void
    {
        if (is_dir($dir)) {
            $this->deleteDirectory($dir);
        }
    }
}
