<?php

// Simple setup script to create localization tables
// This can be accessed via browser at /setup-localization-tables.php

try {
    // Get the database path
    $dbPath = __DIR__ . '/../database/database.sqlite';
    
    if (!file_exists($dbPath)) {
        die('Database file not found at: ' . $dbPath);
    }
    
    // Connect to SQLite database
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Setting up Localization Database Tables...</h2>";
    
    // Create languages table
    echo "<h3>Creating languages table...</h3>";
    $languagesSQL = "
    CREATE TABLE IF NOT EXISTS languages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code VARCHAR(10) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        native_name VARCHAR(255) NOT NULL,
        flag_icon VARCHAR(255),
        direction VARCHAR(3) DEFAULT 'ltr',
        is_active BOOLEAN DEFAULT 1,
        is_default BOOLEAN DEFAULT 0,
        sort_order INTEGER DEFAULT 0,
        locale VARCHAR(10),
        country_code VARCHAR(2),
        currency_code VARCHAR(3),
        date_format VARCHAR(50) DEFAULT 'Y-m-d',
        time_format VARCHAR(50) DEFAULT 'H:i',
        number_format VARCHAR(50) DEFAULT '1,234.56',
        translation_count INTEGER DEFAULT 0,
        completion_percentage DECIMAL(5,2) DEFAULT 0.00,
        metadata TEXT,
        created_at DATETIME,
        updated_at DATETIME
    )";
    
    $pdo->exec($languagesSQL);
    echo "<p>✅ Languages table created</p>";
    
    // Create translation_keys table
    echo "<h3>Creating translation_keys table...</h3>";
    $translationKeysSQL = "
    CREATE TABLE IF NOT EXISTS translation_keys (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key VARCHAR(255) UNIQUE NOT NULL,
        \"group\" VARCHAR(100),
        description TEXT,
        default_value TEXT,
        source_file VARCHAR(255),
        line_number INTEGER,
        is_active BOOLEAN DEFAULT 1,
        is_required BOOLEAN DEFAULT 0,
        type VARCHAR(20) DEFAULT 'text',
        parameters TEXT,
        usage_count INTEGER DEFAULT 0,
        last_used DATETIME,
        first_seen DATETIME,
        translated_count INTEGER DEFAULT 0,
        pending_count INTEGER DEFAULT 0,
        metadata TEXT,
        created_at DATETIME,
        updated_at DATETIME
    )";
    
    $pdo->exec($translationKeysSQL);
    echo "<p>✅ Translation keys table created</p>";
    
    // Create translations table
    echo "<h3>Creating translations table...</h3>";
    $translationsSQL = "
    CREATE TABLE IF NOT EXISTS translations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        language_id INTEGER NOT NULL,
        key VARCHAR(255) NOT NULL,
        value TEXT NOT NULL,
        description TEXT,
        \"group\" VARCHAR(100),
        source_file VARCHAR(255),
        is_active BOOLEAN DEFAULT 1,
        needs_review BOOLEAN DEFAULT 0,
        is_auto_translated BOOLEAN DEFAULT 0,
        status VARCHAR(20) DEFAULT 'draft',
        version INTEGER DEFAULT 1,
        previous_value TEXT,
        last_modified DATETIME,
        last_modified_by INTEGER,
        usage_count INTEGER DEFAULT 0,
        last_used DATETIME,
        character_count INTEGER DEFAULT 0,
        word_count INTEGER DEFAULT 0,
        has_placeholders BOOLEAN DEFAULT 0,
        placeholders TEXT,
        metadata TEXT,
        created_at DATETIME,
        updated_at DATETIME,
        UNIQUE(language_id, key),
        FOREIGN KEY (language_id) REFERENCES languages(id) ON DELETE CASCADE,
        FOREIGN KEY (last_modified_by) REFERENCES users(id) ON DELETE SET NULL
    )";
    
    $pdo->exec($translationsSQL);
    echo "<p>✅ Translations table created</p>";
    
    // Create indexes
    echo "<h3>Creating indexes...</h3>";
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_languages_code ON languages(code)",
        "CREATE INDEX IF NOT EXISTS idx_languages_is_active ON languages(is_active)",
        "CREATE INDEX IF NOT EXISTS idx_languages_is_default ON languages(is_default)",
        "CREATE INDEX IF NOT EXISTS idx_translation_keys_key ON translation_keys(key)",
        "CREATE INDEX IF NOT EXISTS idx_translation_keys_group ON translation_keys(\"group\")",
        "CREATE INDEX IF NOT EXISTS idx_translation_keys_is_active ON translation_keys(is_active)",
        "CREATE INDEX IF NOT EXISTS idx_translations_language_id ON translations(language_id)",
        "CREATE INDEX IF NOT EXISTS idx_translations_key ON translations(key)",
        "CREATE INDEX IF NOT EXISTS idx_translations_group ON translations(\"group\")",
        "CREATE INDEX IF NOT EXISTS idx_translations_status ON translations(status)",
    ];
    
    foreach ($indexes as $indexSQL) {
        $pdo->exec($indexSQL);
    }
    echo "<p>✅ Indexes created</p>";
    
    // Insert sample languages
    echo "<h3>Inserting sample languages...</h3>";
    $sampleLanguages = [
        ['en', 'English', 'English', '🇺🇸', 'ltr', 1, 1, 1],
        ['es', 'Spanish', 'Español', '🇪🇸', 'ltr', 1, 0, 2],
        ['fr', 'French', 'Français', '🇫🇷', 'ltr', 1, 0, 3],
        ['ar', 'Arabic', 'العربية', '🇸🇦', 'rtl', 1, 0, 4],
    ];
    
    $insertedLanguages = 0;
    foreach ($sampleLanguages as $lang) {
        $stmt = $pdo->prepare("SELECT id FROM languages WHERE code = ?");
        $stmt->execute([$lang[0]]);
        if (!$stmt->fetch()) {
            $stmt = $pdo->prepare("
                INSERT INTO languages (code, name, native_name, flag_icon, direction, is_active, is_default, sort_order, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            ");
            $stmt->execute($lang);
            $insertedLanguages++;
            echo "<p>✅ Added language: {$lang[1]}</p>";
        } else {
            echo "<p>⚠️ Language {$lang[1]} already exists</p>";
        }
    }
    
    // Show table status
    echo "<h3>Table Status:</h3>";
    $tables = ['languages', 'translation_keys', 'translations'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>{$table}: {$count} records</p>";
    }
    
    echo "<h3>✅ Localization tables setup completed!</h3>";
    echo "<p>Sample languages have been added. You can now use the localization features.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Enable the localization plugin: <a href='/enable-localization-plugin.php'>enable-localization-plugin.php</a></li>";
    echo "<li>Set up permissions: <a href='/setup-localization-permissions.php'>setup-localization-permissions.php</a></li>";
    echo "<li>Test your application to see the language switcher</li>";
    echo "<li>Delete this file after setup for security</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error:</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
