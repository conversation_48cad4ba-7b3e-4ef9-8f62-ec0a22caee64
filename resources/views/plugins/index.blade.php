@extends('layouts.app')

@section('content')
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        <i class="fas fa-puzzle-piece mr-3 text-primary-600 dark:text-primary-400"></i>
                        Plugin Manager
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300">Manage your application plugins and their dependencies</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('plugins.install') }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                        <i class="fas fa-upload mr-2"></i>
                        Install Plugin
                    </a>
                    <a href="{{ route('plugins.refresh') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 text-white font-medium rounded-md transition duration-150 ease-in-out">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Refresh
                    </a>
                    <a href="{{ route('plugins.api') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium rounded-md transition duration-150 ease-in-out" target="_blank">
                        <i class="fas fa-code mr-2"></i>
                        API
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-cubes text-2xl text-blue-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Plugins</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['total'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-2xl text-green-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Enabled</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['enabled'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-times-circle text-2xl text-red-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Disabled</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['disabled'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dependency Errors -->
        @if(!empty($dependencyErrors))
            <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Dependency Issues Detected</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                @foreach($dependencyErrors as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Plugins List -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Available Plugins</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage your application plugins below</p>
            </div>

            @if(empty($plugins))
                <div class="text-center py-12">
                    <i class="fas fa-puzzle-piece text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Plugins Found</h3>
                    <p class="text-gray-500 mb-4">No plugins are currently installed in the plugins directory.</p>
                    <p class="text-sm text-gray-400">Create a plugin directory in <code class="bg-gray-100 px-2 py-1 rounded">plugins/</code> with a <code class="bg-gray-100 px-2 py-1 rounded">config.json</code> file to get started.</p>
                </div>
            @else
                <ul class="divide-y divide-gray-200">
                    @foreach($plugins as $plugin)
                        <li class="px-4 py-4 sm:px-6 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        @if($plugin->enabled)
                                            <div class="h-3 w-3 bg-green-400 rounded-full"></div>
                                        @else
                                            <div class="h-3 w-3 bg-gray-400 rounded-full"></div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="flex items-center">
                                            <h4 class="text-lg font-medium text-gray-900">{{ $plugin->name }}</h4>
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $plugin->enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                                {{ $plugin->enabled ? 'Enabled' : 'Disabled' }}
                                            </span>
                                            @if($plugin->isSystemPlugin())
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <i class="fas fa-shield-alt mr-1"></i>
                                                    System
                                                </span>
                                            @endif
                                            @if(!$plugin->isValid())
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                                    Invalid
                                                </span>
                                            @endif
                                        </div>
                                        <p class="text-sm text-gray-500 mt-1">{{ $plugin->description ?: 'No description available' }}</p>
                                        <div class="flex items-center mt-2 text-xs text-gray-400 space-x-4">
                                            <span><i class="fas fa-tag mr-1"></i>v{{ $plugin->version }}</span>
                                            @if(!empty($plugin->dependencies))
                                                <span><i class="fas fa-link mr-1"></i>Depends on: {{ implode(', ', $plugin->dependencies) }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('plugins.show', $plugin->name) }}" class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        <i class="fas fa-eye mr-1"></i>
                                        View
                                    </a>
                                    @if($plugin->enabled)
                                        @if($plugin->isSystemPlugin())
                                            <button type="button" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-400 bg-gray-200 cursor-not-allowed" disabled title="System plugins cannot be disabled">
                                                <i class="fas fa-lock mr-1"></i>
                                                System Plugin
                                            </button>
                                        @else
                                            <form method="POST" action="{{ route('plugins.disable', $plugin->name) }}" class="inline">
                                                @csrf
                                                <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" onclick="return confirm('Are you sure you want to disable this plugin?')">
                                                    <i class="fas fa-stop mr-1"></i>
                                                    Disable
                                                </button>
                                            </form>
                                        @endif
                                    @else
                                        <form method="POST" action="{{ route('plugins.enable', $plugin->name) }}" class="inline">
                                            @csrf
                                            <button type="submit" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" {{ !$plugin->isValid() ? 'disabled' : '' }}>
                                                <i class="fas fa-play mr-1"></i>
                                                Enable
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            @endif
    </div>
@endsection
