@extends('layouts.app')

@section('content')
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <nav class="flex" aria-label="Breadcrumb">
                        <ol class="flex items-center space-x-4">
                            <li>
                                <a href="{{ route('plugins.index') }}" class="text-gray-400 hover:text-gray-500">
                                    <i class="fas fa-puzzle-piece"></i>
                                    <span class="sr-only">Plugins</span>
                                </a>
                            </li>
                            <li>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400 mr-4"></i>
                                    <span class="text-sm font-medium text-gray-500">{{ $plugin->name }}</span>
                                </div>
                            </li>
                        </ol>
                    </nav>
                    <h1 class="text-3xl font-bold text-gray-900 mt-2 mb-2">
                        {{ $plugin->name }}
                        <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $plugin->enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $plugin->enabled ? 'Enabled' : 'Disabled' }}
                        </span>
                        @if($plugin->isSystemPlugin())
                            <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-shield-alt mr-2"></i>
                                System Plugin
                            </span>
                        @endif
                    </h1>
                    <p class="text-gray-600">{{ $plugin->description ?: 'No description available' }}</p>
                </div>
                <div class="flex space-x-3">
                    <!-- Export Plugin -->
                    <a href="{{ route('plugins.export', $plugin->name) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                        <i class="fas fa-download mr-2"></i>
                        Export
                    </a>

                    @if($plugin->enabled)
                        @if($plugin->isSystemPlugin())
                            <button type="button" class="inline-flex items-center px-4 py-2 bg-gray-400 text-white font-medium rounded-md cursor-not-allowed" disabled title="System plugins cannot be disabled">
                                <i class="fas fa-lock mr-2"></i>
                                System Plugin
                            </button>
                        @else
                            <form method="POST" action="{{ route('plugins.disable', $plugin->name) }}" class="inline">
                                @csrf
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition duration-150 ease-in-out" onclick="return confirm('Are you sure you want to disable this plugin?')">
                                    <i class="fas fa-stop mr-2"></i>
                                    Disable Plugin
                                </button>
                            </form>
                        @endif
                    @else
                        <form method="POST" action="{{ route('plugins.enable', $plugin->name) }}" class="inline">
                            @csrf
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-150 ease-in-out" {{ !$plugin->isValid() ? 'disabled' : '' }}>
                                <i class="fas fa-play mr-2"></i>
                                Enable Plugin
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Plugin Information</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">Basic details about this plugin</p>
                    </div>
                    <div class="border-t border-gray-200">
                        <dl>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $plugin->name }}</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Version</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $plugin->version }}</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $plugin->enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $plugin->enabled ? 'Enabled' : 'Disabled' }}
                                    </span>
                                </dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $plugin->description ?: 'No description provided' }}</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Path</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                    <code class="bg-gray-100 px-2 py-1 rounded text-xs">{{ $plugin->path }}</code>
                                </dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Valid</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                    @if($plugin->isValid())
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>
                                            Valid
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times mr-1"></i>
                                            Invalid
                                        </span>
                                    @endif
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Dependencies -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Dependencies</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">Plugins that this plugin depends on</p>
                    </div>
                    <div class="border-t border-gray-200">
                        @if(empty($plugin->dependencies))
                            <div class="px-4 py-5 text-center text-gray-500">
                                <i class="fas fa-info-circle text-2xl mb-2"></i>
                                <p>This plugin has no dependencies</p>
                            </div>
                        @else
                            <ul class="divide-y divide-gray-200">
                                @foreach($plugin->dependencies as $dependency)
                                    <li class="px-4 py-4 flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i class="fas fa-puzzle-piece text-gray-400 mr-3"></i>
                                            <span class="text-sm font-medium text-gray-900">{{ $dependency }}</span>
                                        </div>
                                        <div>
                                            <!-- You could add dependency status here -->
                                            <span class="text-xs text-gray-500">Required</span>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                </div>

                <!-- Danger Zone -->
                @if(!$plugin->isSystemPlugin())
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg border-l-4 border-red-400">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-red-900">Danger Zone</h3>
                            <p class="mt-1 max-w-2xl text-sm text-red-600">Irreversible and destructive actions.</p>
                        </div>
                        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-red-900">Uninstall this plugin</h4>
                                    <p class="text-sm text-red-600">Permanently remove this plugin and optionally clear all its data.</p>
                                </div>
                                <button onclick="showUninstallModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <i class="fas fa-trash mr-2"></i>
                                    Uninstall Plugin
                                </button>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Plugin Structure -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Plugin Structure</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">Available components</p>
                    </div>
                    <div class="border-t border-gray-200">
                        <ul class="divide-y divide-gray-200">
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-cog text-gray-400 mr-3"></i>
                                    <span class="text-sm text-gray-900">Controllers</span>
                                </div>
                                @if($plugin->hasControllers())
                                    <i class="fas fa-check text-green-500"></i>
                                @else
                                    <i class="fas fa-times text-gray-300"></i>
                                @endif
                            </li>
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-database text-gray-400 mr-3"></i>
                                    <span class="text-sm text-gray-900">Models</span>
                                </div>
                                @if($plugin->hasModels())
                                    <i class="fas fa-check text-green-500"></i>
                                @else
                                    <i class="fas fa-times text-gray-300"></i>
                                @endif
                            </li>
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-eye text-gray-400 mr-3"></i>
                                    <span class="text-sm text-gray-900">Views</span>
                                </div>
                                @if($plugin->hasViews())
                                    <i class="fas fa-check text-green-500"></i>
                                @else
                                    <i class="fas fa-times text-gray-300"></i>
                                @endif
                            </li>
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-arrow-up text-gray-400 mr-3"></i>
                                    <span class="text-sm text-gray-900">Migrations</span>
                                </div>
                                @if($plugin->hasMigrations())
                                    <i class="fas fa-check text-green-500"></i>
                                @else
                                    <i class="fas fa-times text-gray-300"></i>
                                @endif
                            </li>
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-seedling text-gray-400 mr-3"></i>
                                    <span class="text-sm text-gray-900">Seeds</span>
                                </div>
                                @if($plugin->hasSeeds())
                                    <i class="fas fa-check text-green-500"></i>
                                @else
                                    <i class="fas fa-times text-gray-300"></i>
                                @endif
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Data Management -->
                @if($plugin->enabled && ($hasSeeder || $totalDataCount > 0))
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Data Management</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage plugin data and sample content</p>
                        </div>
                        <div class="border-t border-gray-200">
                            <!-- Plugin Statistics -->
                            @if(!empty($stats) && $totalDataCount > 0)
                                <div class="px-4 py-4 border-b border-gray-200">
                                    <h4 class="text-sm font-medium text-gray-700 mb-3">Current Data</h4>
                                    <div class="grid grid-cols-2 gap-3">
                                        @foreach($stats as $key => $count)
                                            @if($count > 0)
                                                <div class="text-center p-3 bg-gray-50 rounded border">
                                                    <div class="text-lg font-semibold text-gray-900">{{ $count }}</div>
                                                    <div class="text-xs text-gray-500">{{ ucfirst(str_replace('_', ' ', $key)) }}</div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Data Management Actions -->
                            <div class="px-4 py-4 space-y-3">
                                @if($hasSeeder)
                                    <button onclick="seedPluginData('{{ $plugin->name }}', '{{ $seederClass }}')"
                                            class="w-full inline-flex justify-center items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                                        <i class="fas fa-seedling mr-2"></i>
                                        Seed Sample Data
                                    </button>
                                @endif

                                @if($totalDataCount > 0)
                                    <button onclick="clearPluginData('{{ $plugin->name }}', {{ json_encode($tables) }})"
                                            class="w-full inline-flex justify-center items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                                        <i class="fas fa-trash mr-2"></i>
                                        Clear All Data
                                    </button>
                                @endif

                                @if(!$hasSeeder && $totalDataCount === 0)
                                    <div class="text-center py-4 text-gray-500 text-sm">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        No data management options available
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Quick Actions -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
                    </div>
                    <div class="border-t border-gray-200">
                        <div class="px-4 py-4 space-y-3">
                            <a href="{{ route('plugins.index') }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back to Plugins
                            </a>
                        </div>
                    </div>
                </div>


            </div>
    </div>

<!-- Uninstall Modal -->
<div id="uninstallModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-center mx-auto w-12 h-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Uninstall Plugin</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        Are you sure you want to uninstall the <strong>{{ $plugin->name }}</strong> plugin? This action cannot be undone.
                    </p>
                    <div class="mt-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="clearDataCheckbox" class="rounded border-gray-300 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-600">Also clear all plugin data from database</span>
                        </label>
                    </div>
                </div>
                <div class="items-center px-4 py-3">
                    <button onclick="hideUninstallModal()" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                        Cancel
                    </button>
                    <button onclick="confirmUninstall()" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                        Uninstall
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Seed plugin data
function seedPluginData(pluginName, seederClass) {
    if (confirm(`This will create sample data for the ${pluginName} plugin. Continue?`)) {
        showLoadingState('seeding', pluginName);

        fetch(`/plugins/${pluginName}/seed-sample-data`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingState();
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoadingState();
            showToast('Error: ' + error.message, 'error');
        });
    }
}

// Clear plugin data
function clearPluginData(pluginName, tables) {
    if (confirm(`⚠️ WARNING: This will permanently delete ALL ${pluginName} data. This action cannot be undone. Are you absolutely sure?`)) {
        if (confirm(`This is your final confirmation. All ${pluginName} plugin data will be permanently deleted. Continue?`)) {
            showLoadingState('clearing', pluginName);

            fetch(`/plugins/${pluginName}/clear-all-data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingState();
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showToast('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideLoadingState();
                showToast('Error: ' + error.message, 'error');
            });
        }
    }
}

// Loading state functions
function showLoadingState(action, pluginName) {
    if (!document.getElementById('plugin-loading-overlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'plugin-loading-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                    <span id="loading-text" class="text-gray-700 font-medium">Processing...</span>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);
    }

    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
        const actionText = action === 'seeding' ? 'Seeding sample data' : 'Clearing plugin data';
        loadingText.textContent = `${actionText} for ${pluginName}...`;
    }
}

function hideLoadingState() {
    const overlay = document.getElementById('plugin-loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// Toast notification functions
function showToast(message, type) {
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(toastContainer);
    }

    const toast = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg flex items-center max-w-sm transform transition-all duration-300 translate-x-full opacity-0`;
    toast.innerHTML = `
        <i class="fas ${icon} mr-2"></i>
        <span class="flex-1">${message}</span>
        <button onclick="this.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
            <i class="fas fa-times"></i>
        </button>
    `;

    toastContainer.appendChild(toast);

    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => toast.remove(), 300);
        }
    }, 5000);
}

// Uninstall modal functions
function showUninstallModal() {
    document.getElementById('uninstallModal').classList.remove('hidden');
}

function hideUninstallModal() {
    document.getElementById('uninstallModal').classList.add('hidden');
}

function confirmUninstall() {
    const clearData = document.getElementById('clearDataCheckbox').checked;
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/plugins/{{ $plugin->name }}/uninstall`;

    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);

    if (clearData) {
        const clearDataInput = document.createElement('input');
        clearDataInput.type = 'hidden';
        clearDataInput.name = 'clear_data';
        clearDataInput.value = '1';
        form.appendChild(clearDataInput);
    }

    document.body.appendChild(form);
    form.submit();
}
</script>
@endsection
