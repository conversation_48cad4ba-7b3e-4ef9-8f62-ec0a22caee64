navigation items is not found after last update 

i can remove a permission 

i can change the dark theme from the settings only remove the button in the side menu 

fix the colors of dark mode 

---------------------------------------------------------------------------------------------------

create a new plugin called todo 
when done hide , ican show done item by toggle show done 
i can order them by drag and drop , edit or delete 

---------------------------------------------------------------------------------------------------


create a new plugin called localization 
i can add new language 
i can translate the app to the new language 
i can change the language of the app from settings 


---------------------------------------------------------------------------------------------------

i would like to create a new plugin admin 
make sure to add needed permissions and add in side menu 

the plugin use external api , this api is been protected by session 
to create a session i need to call login api with username and password as below which will send a otp to my phone 

curl 'https://cp.taqnyat.sa/login.php' \
-X POST \
-H 'Host: cp.taqnyat.sa' \
-H 'Connection: keep-alive' \
-H 'Authorization: Basic YWRtaW46W0tLMmoqKioxOTExOSkpMTFdXQ==' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
-H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'Accept: */*' \
-H 'Origin: https://cp.taqnyat.sa' \                      
-H 'Sec-Fetch-Site: same-origin' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Referer: https://cp.taqnyat.sa/index.php' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Content-Type: application/x-www-form-urlencoded' \
--cookie '_clck=py1yak%7C2%7Cfwn%7C0%7C1953; PHPSESSID=p04ljtc02pngt5l1m9oah83t13' \
--data-raw 'userName=a.younis&password=Anas@123456789' \

when the code recived i need to input the code using the below curl 
curl 'https://cp.taqnyat.sa/login.php' \
-X POST \
-H 'Host: cp.taqnyat.sa' \
-H 'Connection: keep-alive' \
-H 'Authorization: Basic YWRtaW46W0tLMmoqKioxOTExOSkpMTFdXQ==' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
-H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'Accept: */*' \
-H 'Origin: https://cp.taqnyat.sa' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Referer: https://cp.taqnyat.sa/index.php' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Content-Type: application/x-www-form-urlencoded' \
--cookie '_clck=py1yak%7C2%7Cfwn%7C0%7C1953; PHPSESSID=p04ljtc02pngt5l1m9oah83t13' \
--data-raw 'userName=a.younis&password=Anas@123456789&otp=7979' \

if the response was "loginSuccess" mean i created a session 

i need to save the parameter in the cookies "PHPSESSID" and its value to use it for future requests 







    > crm [potential clients]
    > exsist clients 
    > churn clients 
    > invoices
    > business info

use it to build the needed component for the plugin
    

---------------------------------------------------------------------------------------------------

create a new menu called configration below plugin manager 
this menu responsible for user preferances like dark and light mode 
prefered language 
browser notifications 
sound notifications 
date format 
time format 
timezone 

also create a localization plugin that handle the language and rtl and ltr and add new language 
make sure to add needed permissions and add in side menu 

---------------------------------------------------------------------------------------------------


i would like to create a new plugin tenders 
https://tenders.etimad.sa/Tender/AllTendersForVisitor?PageNumber=1

make sure to add needed permissions and add in side menu 

---------------------------------------------------------------------------------------------------

i would like to create a new plugin 36dialog
    > traffic 
    > businesses info 

make sure to add needed permissions and add in side menu 


---------------------------------------------------------------------------------------------------
i would like to create a new plugin  meetings , Event
    > each meeting can be schedualed with agenda , and prepration 
        date , time , location , participants , and reminder
    > after the meeting has minuts of meeting , actions , and files and media (audio , video , images , documents )

each user should see his meeting only or the meetings he been set as a watcher with 
make sure to add needed permissions and add in side menu 

---------------------------------------------------------------------------------------------------

i would like to create a new plugin documentation versioning 
make sure to add needed permissions and add in side menu 

---------------------------------------------------------------------------------------------------

i would like to create a new plugin emails 
make sure to add needed permissions and add in side menu 

---------------------------------------------------------------------------------------------------

i would like to create a new plugin ai 
make sure to add needed permissions and add in side menu 

---------------------------------------------------------------------------------------------------

i would like to create a new plugin compititors
make sure to add needed permissions and add in side menu 

---------------------------------------------------------------------------------------------------

you must to create a item in the plugins list , a side menu , and permissions related to the plugin 

data source 
    > click up 
    > 360dialog 
    > emails
    > taqnyat admin 
    > excel sheet 
    > meta business
    > tenders
    > whatsapp , 