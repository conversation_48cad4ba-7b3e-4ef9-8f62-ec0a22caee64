<?php

namespace Plugins\Settings\Controllers;

use App\Http\Controllers\Controller;
use App\Traits\DatabaseCompatibility;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use ZipArchive;

class BackupController extends Controller
{
    use DatabaseCompatibility;

    private string $backupPath;
    private int $maxBackups;
    private int $retentionDays;

    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->hasPermission('manage_settings')) {
                abort(403, 'Access denied. You do not have permission to manage settings.');
            }

            return $next($request);
        });

        $this->backupPath = storage_path('app/backups');
        $this->maxBackups = config('settings.max_backup_files', 10);
        $this->retentionDays = config('settings.backup_retention_days', 30);

        // Ensure backup directory exists
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }

    /**
     * Display backup management interface
     */
    public function index(): View
    {
        $backups = $this->getBackupList();
        $stats = $this->getBackupStats();
        
        return view('plugins.settings::backups', compact('backups', 'stats'));
    }

    /**
     * Create a new backup
     */
    public function create(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'include_files' => 'boolean',
                'description' => 'nullable|string|max:255',
            ]);

            $includeFiles = $request->boolean('include_files', true);
            $description = $request->input('description', '');
            
            $backupResult = $this->createBackup($includeFiles, $description);
            
            Log::info('Backup created', [
                'backup_file' => $backupResult['filename'],
                'include_files' => $includeFiles,
                'user' => auth()->user()->email,
                'description' => $description,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Backup created successfully',
                'backup' => $backupResult,
            ]);
        } catch (\Exception $e) {
            Log::error('Backup creation failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Backup creation failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download a backup file
     */
    public function download(Request $request, string $backup): Response
    {
        try {
            $backupFile = $this->backupPath . '/' . $backup;
            
            if (!file_exists($backupFile) || !$this->isValidBackupFile($backup)) {
                abort(404, 'Backup file not found');
            }

            Log::info('Backup downloaded', [
                'backup_file' => $backup,
                'user' => auth()->user()->email,
            ]);

            return response()->download($backupFile);
        } catch (\Exception $e) {
            Log::error('Backup download failed: ' . $e->getMessage());
            abort(500, 'Failed to download backup');
        }
    }

    /**
     * Restore from backup
     */
    public function restore(Request $request, string $backup): JsonResponse
    {
        try {
            $request->validate([
                'confirm' => 'required|boolean|accepted',
            ]);

            $backupFile = $this->backupPath . '/' . $backup;
            
            if (!file_exists($backupFile) || !$this->isValidBackupFile($backup)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found',
                ], 404);
            }

            $restoreResult = $this->restoreBackup($backupFile);
            
            Log::info('Backup restore attempted', [
                'backup_file' => $backup,
                'user' => auth()->user()->email,
                'success' => $restoreResult['success'],
            ]);

            return response()->json($restoreResult);
        } catch (\Exception $e) {
            Log::error('Backup restore failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Backup restore failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a backup file
     */
    public function destroy(Request $request, string $backup): JsonResponse
    {
        try {
            $backupFile = $this->backupPath . '/' . $backup;
            
            if (!file_exists($backupFile) || !$this->isValidBackupFile($backup)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found',
                ], 404);
            }

            unlink($backupFile);
            
            Log::info('Backup deleted', [
                'backup_file' => $backup,
                'user' => auth()->user()->email,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Backup deleted successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('Backup deletion failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete backup: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get backup status
     */
    public function status(Request $request, string $backup): JsonResponse
    {
        try {
            $backupFile = $this->backupPath . '/' . $backup;
            
            if (!file_exists($backupFile) || !$this->isValidBackupFile($backup)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found',
                ], 404);
            }

            $info = $this->getBackupInfo($backupFile);

            return response()->json([
                'success' => true,
                'backup_info' => $info,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get backup status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a backup
     */
    private function createBackup(bool $includeFiles = true, string $description = ''): array
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "backup-{$timestamp}.zip";
        $backupFile = $this->backupPath . '/' . $filename;

        $zip = new ZipArchive();
        if ($zip->open($backupFile, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('Cannot create backup file');
        }

        try {
            // Create database dump
            $databaseDump = $this->createDatabaseDump();
            $zip->addFromString('database.sql', $databaseDump);

            // Add metadata
            $metadata = [
                'created_at' => now()->toISOString(),
                'created_by' => auth()->user()->email,
                'app_version' => config('app.version', '1.0.0'),
                'description' => $description,
                'includes_files' => $includeFiles,
                'database_driver' => config('database.default'),
            ];
            $zip->addFromString('metadata.json', json_encode($metadata, JSON_PRETTY_PRINT));

            // Add application files if requested
            if ($includeFiles) {
                $this->addFilesToBackup($zip);
            }

            $zip->close();

            // Clean up old backups
            $this->cleanupOldBackups();

            return [
                'filename' => $filename,
                'size' => filesize($backupFile),
                'created_at' => now()->toISOString(),
                'includes_files' => $includeFiles,
                'description' => $description,
            ];
        } catch (\Exception $e) {
            $zip->close();
            if (file_exists($backupFile)) {
                unlink($backupFile);
            }
            throw $e;
        }
    }

    /**
     * Create database dump
     */
    private function createDatabaseDump(): string
    {
        $driver = config('database.default');
        
        switch ($driver) {
            case 'mysql':
                return $this->createMySQLDump();
            case 'sqlite':
                return $this->createSQLiteDump();
            case 'pgsql':
                return $this->createPostgreSQLDump();
            default:
                throw new \Exception("Unsupported database driver: {$driver}");
        }
    }

    /**
     * Create MySQL dump
     */
    private function createMySQLDump(): string
    {
        $tables = DB::select('SHOW TABLES');
        $dump = "-- MySQL Database Dump\n";
        $dump .= "-- Generated on: " . now()->toDateTimeString() . "\n\n";
        
        foreach ($tables as $table) {
            $tableName = array_values((array) $table)[0];
            
            // Get table structure
            $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`")[0];
            $dump .= "-- Table: {$tableName}\n";
            $dump .= $createTable->{'Create Table'} . ";\n\n";
            
            // Get table data
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $dump .= "-- Data for table: {$tableName}\n";
                foreach ($rows as $row) {
                    $values = array_map(function($value) {
                        return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);
                    
                    $dump .= "INSERT INTO `{$tableName}` VALUES (" . implode(', ', $values) . ");\n";
                }
                $dump .= "\n";
            }
        }
        
        return $dump;
    }

    /**
     * Create SQLite dump
     */
    private function createSQLiteDump(): string
    {
        $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
        $dump = "-- SQLite Database Dump\n";
        $dump .= "-- Generated on: " . now()->toDateTimeString() . "\n\n";
        
        foreach ($tables as $table) {
            $tableName = $table->name;
            
            // Get table structure
            $createTable = DB::select("SELECT sql FROM sqlite_master WHERE type='table' AND name=?", [$tableName])[0];
            $dump .= "-- Table: {$tableName}\n";
            $dump .= $createTable->sql . ";\n\n";
            
            // Get table data
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $dump .= "-- Data for table: {$tableName}\n";
                foreach ($rows as $row) {
                    $values = array_map(function($value) {
                        return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);
                    
                    $dump .= "INSERT INTO \"{$tableName}\" VALUES (" . implode(', ', $values) . ");\n";
                }
                $dump .= "\n";
            }
        }
        
        return $dump;
    }

    /**
     * Create PostgreSQL dump
     */
    private function createPostgreSQLDump(): string
    {
        // This is a simplified version - in production, you might want to use pg_dump
        $tables = DB::select("SELECT tablename FROM pg_tables WHERE schemaname = 'public'");
        $dump = "-- PostgreSQL Database Dump\n";
        $dump .= "-- Generated on: " . now()->toDateTimeString() . "\n\n";
        
        foreach ($tables as $table) {
            $tableName = $table->tablename;
            
            // Get table data (structure would require more complex queries)
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $dump .= "-- Data for table: {$tableName}\n";
                foreach ($rows as $row) {
                    $values = array_map(function($value) {
                        return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);
                    
                    $dump .= "INSERT INTO \"{$tableName}\" VALUES (" . implode(', ', $values) . ");\n";
                }
                $dump .= "\n";
            }
        }
        
        return $dump;
    }

    /**
     * Add files to backup
     */
    private function addFilesToBackup(ZipArchive $zip): void
    {
        $filesToBackup = [
            'app',
            'config',
            'database/migrations',
            'resources',
            'routes',
            'plugins',
            '.env',
            'composer.json',
            'composer.lock',
        ];

        foreach ($filesToBackup as $path) {
            $fullPath = base_path($path);
            if (file_exists($fullPath)) {
                if (is_file($fullPath)) {
                    $zip->addFile($fullPath, $path);
                } else {
                    $this->addDirectoryToZip($zip, $fullPath, $path);
                }
            }
        }
    }

    /**
     * Add directory to zip recursively
     */
    private function addDirectoryToZip(ZipArchive $zip, string $dir, string $zipPath): void
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relativePath = $zipPath . '/' . $iterator->getSubPathName();
                $zip->addFile($file->getRealPath(), $relativePath);
            }
        }
    }

    /**
     * Get list of backups
     */
    private function getBackupList(): array
    {
        $files = glob($this->backupPath . '/*.zip');
        $backups = [];

        foreach ($files as $file) {
            $filename = basename($file);
            $backups[] = [
                'filename' => $filename,
                'size' => filesize($file),
                'created_at' => date('Y-m-d H:i:s', filemtime($file)),
                'info' => $this->getBackupInfo($file),
            ];
        }

        // Sort by creation date (newest first)
        usort($backups, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $backups;
    }

    /**
     * Get backup statistics
     */
    private function getBackupStats(): array
    {
        $files = glob($this->backupPath . '/*.zip');
        $totalSize = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
        }

        return [
            'total_backups' => count($files),
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'oldest_backup' => count($files) > 0 ? date('Y-m-d H:i:s', min(array_map('filemtime', $files))) : null,
            'newest_backup' => count($files) > 0 ? date('Y-m-d H:i:s', max(array_map('filemtime', $files))) : null,
        ];
    }

    /**
     * Get backup information
     */
    private function getBackupInfo(string $backupFile): array
    {
        try {
            $zip = new ZipArchive();
            if ($zip->open($backupFile) === TRUE) {
                $metadata = $zip->getFromName('metadata.json');
                $zip->close();
                
                if ($metadata) {
                    return json_decode($metadata, true);
                }
            }
        } catch (\Exception $e) {
            // Ignore errors and return basic info
        }

        return [
            'created_at' => date('Y-m-d H:i:s', filemtime($backupFile)),
            'size' => filesize($backupFile),
        ];
    }

    /**
     * Restore from backup
     */
    private function restoreBackup(string $backupFile): array
    {
        try {
            $zip = new ZipArchive();
            if ($zip->open($backupFile) !== TRUE) {
                throw new \Exception('Cannot open backup file');
            }

            $tempDir = storage_path('app/temp/restore-' . time());
            mkdir($tempDir, 0755, true);

            try {
                // Extract backup
                $zip->extractTo($tempDir);
                $zip->close();

                // Restore database
                $databaseFile = $tempDir . '/database.sql';
                if (file_exists($databaseFile)) {
                    $this->restoreDatabase($databaseFile);
                }

                // Clear caches
                Artisan::call('cache:clear');
                Artisan::call('config:clear');
                Artisan::call('view:clear');

                return [
                    'success' => true,
                    'message' => 'Backup restored successfully',
                ];
            } finally {
                // Clean up temp directory
                $this->deleteDirectory($tempDir);
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Restore failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Restore database from SQL file
     */
    private function restoreDatabase(string $sqlFile): void
    {
        $sql = file_get_contents($sqlFile);
        
        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !str_starts_with($stmt, '--');
            }
        );

        DB::transaction(function() use ($statements) {
            foreach ($statements as $statement) {
                if (!empty(trim($statement))) {
                    DB::unprepared($statement);
                }
            }
        });
    }

    /**
     * Clean up old backups
     */
    private function cleanupOldBackups(): void
    {
        $files = glob($this->backupPath . '/*.zip');
        
        // Remove files older than retention period
        $cutoffTime = time() - ($this->retentionDays * 24 * 60 * 60);
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
            }
        }

        // Remove excess files (keep only max number)
        $files = glob($this->backupPath . '/*.zip');
        if (count($files) > $this->maxBackups) {
            // Sort by modification time (oldest first)
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            $filesToDelete = array_slice($files, 0, count($files) - $this->maxBackups);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }

    /**
     * Check if backup file is valid
     */
    private function isValidBackupFile(string $filename): bool
    {
        return preg_match('/^backup-\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.zip$/', $filename) ||
               preg_match('/^pre-update-backup-\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.zip$/', $filename);
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Delete directory recursively
     */
    private function deleteDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }
}
