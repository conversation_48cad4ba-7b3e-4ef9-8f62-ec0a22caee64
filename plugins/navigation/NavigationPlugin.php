<?php

namespace Plugins\Navigation;

use App\Contracts\PluginInterface;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;

class NavigationPlugin extends ServiceProvider implements PluginInterface
{
    public function boot()
    {
        $this->loadRoutes();
        $this->loadViews();
        $this->loadMigrations();
    }

    public function register()
    {
        // Register any services
    }

    public function getName(): string
    {
        return 'navigation';
    }

    public function getDisplayName(): string
    {
        return 'Navigation Manager';
    }

    public function getDescription(): string
    {
        return 'Manage navigation menus and structure';
    }

    public function getVersion(): string
    {
        return '1.0.0';
    }

    public function getAuthor(): string
    {
        return 'Business System';
    }

    public function getDependencies(): array
    {
        return [];
    }

    public function getPermissions(): array
    {
        return [
            'manage_navigation' => 'Manage Navigation',
            'view_navigation' => 'View Navigation',
        ];
    }

    public function getMenuItems(): array
    {
        return [
            [
                'label' => 'Navigation',
                'icon' => 'fas fa-sitemap',
                'url' => '/navigation',
                'permissions' => ['view_navigation'],
                'sort_order' => 10,
            ]
        ];
    }

    public function install(): bool
    {
        // Run migrations
        $this->runMigrations();
        
        // Seed permissions
        $this->seedPermissions();
        
        return true;
    }

    public function uninstall(): bool
    {
        // Remove plugin data if needed
        return true;
    }

    public function enable(): bool
    {
        return true;
    }

    public function disable(): bool
    {
        return true;
    }

    public function isEnabled(): bool
    {
        return true;
    }

    private function loadRoutes()
    {
        if (file_exists(__DIR__ . '/Routes/web.php')) {
            Route::middleware(['web', 'auth'])
                ->prefix('')
                ->group(__DIR__ . '/Routes/web.php');
        }
    }

    private function loadViews()
    {
        $this->loadViewsFrom(__DIR__ . '/Views', 'navigation');
    }

    private function loadMigrations()
    {
        $this->loadMigrationsFrom(__DIR__ . '/Database/Migrations');
    }

    private function runMigrations()
    {
        // This would be handled by the plugin manager
    }

    private function seedPermissions()
    {
        // This would be handled by the plugin manager
    }
}
