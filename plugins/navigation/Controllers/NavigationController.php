<?php

namespace Plugins\Navigation\Controllers;

use App\Http\Controllers\Controller;
use App\Models\NavigationMenu;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NavigationController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);

            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for each action
     */
    protected function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_navigation',
            'tree' => null, // No permission check - used for rendering navigation
            'store' => 'manage_navigation',
            'update' => 'manage_navigation',
            'destroy' => 'manage_navigation',
            'updateOrder' => 'manage_navigation',
            'reset' => 'manage_navigation',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display the navigation management interface
     */
    public function index(): View
    {
        $menuItems = NavigationMenu::with(['parent', 'children'])
            ->orderBy('sort_order')
            ->get();

        $rootItems = NavigationMenu::root()
            ->ordered()
            ->with(['children' => function ($query) {
                $query->ordered();
            }])
            ->get();

        // Get available plugins for menu creation
        $pluginManager = app(\App\Services\PluginManager::class);
        $availablePlugins = $pluginManager->getEnabledPlugins();

        // Get available permissions for permission selector
        $availablePermissions = \App\Models\Permission::orderBy('display_name')->get();

        return view('plugins.navigation::index', compact('menuItems', 'rootItems', 'availablePlugins', 'availablePermissions'));
    }

    /**
     * Store a new navigation menu item
     */
    public function store(Request $request): JsonResponse
    {
        // Custom validation based on type
        $type = $request->input('type', 'link');

        $rules = [
            'name' => 'required|string|max:255|unique:navigation_menus',
            'type' => 'nullable|string|in:link,separator',
            'icon' => 'nullable|string|max:255',
            'url' => 'nullable|string|max:255',
            'route' => 'nullable|string|max:255',
            'plugin' => 'nullable|string|max:255',
            'permissions' => 'nullable|array',
            'parent_id' => 'nullable|exists:navigation_menus,id',
            'target' => 'nullable|string|in:_self,_blank,_parent,_top',
            'visible' => 'boolean',
        ];

        // Label is required for links but optional for separators
        if ($type === 'separator') {
            $rules['label'] = 'nullable|string|max:255';
        } else {
            $rules['label'] = 'required|string|max:255';
        }

        $request->validate($rules);

        try {
            $sortOrder = NavigationMenu::getNextSortOrder($request->parent_id);

            $menuItem = NavigationMenu::create([
                'name' => $request->name,
                'type' => $request->type ?? 'link',
                'label' => $request->label,
                'icon' => $request->icon,
                'url' => $request->url,
                'route' => $request->route,
                'plugin' => $request->plugin,
                'permissions' => $request->permissions,
                'parent_id' => $request->parent_id,
                'sort_order' => $sortOrder,
                'target' => $request->target ?? '_self',
                'is_active' => true,
                'visible' => $request->boolean('visible', true),
                'is_system' => false,
            ]);

            Log::info("Navigation menu item '{$menuItem->name}' created", [
                'user' => auth()->user()->email,
                'menu_item_id' => $menuItem->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Menu item created successfully.',
                'menu_item' => $menuItem->load(['parent', 'children']),
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to create navigation menu item: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to create menu item: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a navigation menu item
     */
    public function update(Request $request, NavigationMenu $navigationMenu): JsonResponse
    {
        // Custom validation based on type
        $type = $request->input('type', 'link');

        $rules = [
            'name' => 'required|string|max:255|unique:navigation_menus,name,' . $navigationMenu->id,
            'type' => 'nullable|string|in:link,separator',
            'icon' => 'nullable|string|max:255',
            'url' => 'nullable|string|max:255',
            'route' => 'nullable|string|max:255',
            'plugin' => 'nullable|string|max:255',
            'permissions' => 'nullable|array',
            'parent_id' => 'nullable|exists:navigation_menus,id',
            'target' => 'nullable|string|in:_self,_blank,_parent,_top',
            'is_active' => 'boolean',
            'visible' => 'boolean',
        ];

        // Label is required for links but optional for separators
        if ($type === 'separator') {
            $rules['label'] = 'nullable|string|max:255';
        } else {
            $rules['label'] = 'required|string|max:255';
        }

        $request->validate($rules);

        try {
            // Prevent making a menu item its own parent or creating circular references
            if ($request->parent_id && $this->wouldCreateCircularReference($navigationMenu->id, $request->parent_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot create circular reference in menu hierarchy.',
                ], 400);
            }

            $navigationMenu->update([
                'name' => $request->name,
                'type' => $request->type ?? 'link',
                'label' => $request->label,
                'icon' => $request->icon,
                'url' => $request->url,
                'route' => $request->route,
                'plugin' => $request->plugin,
                'permissions' => $request->permissions,
                'parent_id' => $request->parent_id,
                'target' => $request->target ?? '_self',
                'is_active' => $request->boolean('is_active', true),
                'visible' => $request->boolean('visible', true),
            ]);

            Log::info("Navigation menu item '{$navigationMenu->name}' updated", [
                'user' => auth()->user()->email,
                'menu_item_id' => $navigationMenu->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Menu item updated successfully.',
                'menu_item' => $navigationMenu->fresh(['parent', 'children']),
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to update navigation menu item: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update menu item: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a navigation menu item
     */
    public function destroy(NavigationMenu $navigationMenu): JsonResponse
    {
        try {
            // Prevent deleting system menu items
            if ($navigationMenu->is_system) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete system menu items.',
                ], 400);
            }

            $name = $navigationMenu->name;
            $label = $navigationMenu->label;

            // Count children that will be deleted (due to cascade)
            $childrenCount = $navigationMenu->children()->count();

            DB::transaction(function () use ($navigationMenu) {
                $navigationMenu->delete();
            });

            $message = "Menu item '{$label}' deleted successfully.";
            if ($childrenCount > 0) {
                $message .= " {$childrenCount} sub-menu item(s) were also deleted.";
            }

            Log::info("Navigation menu item '{$name}' deleted", [
                'user' => auth()->user()->email,
                'children_deleted' => $childrenCount,
            ]);

            return response()->json([
                'success' => true,
                'message' => $message,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to delete navigation menu item: " . $e->getMessage(), [
                'menu_id' => $navigationMenu->id,
                'user' => auth()->user()->email,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete menu item: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update the sort order of menu items
     */
    public function updateOrder(Request $request): JsonResponse
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:navigation_menus,id',
            'items.*.sort_order' => 'required|integer|min:0',
            'items.*.parent_id' => 'nullable|exists:navigation_menus,id',
        ]);

        try {
            DB::transaction(function () use ($request) {
                foreach ($request->items as $item) {
                    NavigationMenu::where('id', $item['id'])->update([
                        'sort_order' => $item['sort_order'],
                        'parent_id' => $item['parent_id'],
                    ]);
                }
            });

            Log::info("Navigation menu order updated", [
                'user' => auth()->user()->email,
                'items_count' => count($request->items),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Menu order updated successfully.',
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to update navigation menu order: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update menu order: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reset navigation to default (plugin-based) structure
     */
    public function reset(): JsonResponse
    {
        try {
            DB::transaction(function () {
                // Delete all existing menu items (both system and non-system)
                NavigationMenu::truncate();

                // Regenerate complete menu structure from plugins
                $this->generateDefaultNavigation();
            });

            Log::info("Navigation menu reset to default", [
                'user' => auth()->user()->email,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Navigation reset to default structure.',
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to reset navigation menu: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to reset navigation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get navigation tree as JSON for frontend
     */
    public function tree(): JsonResponse
    {
        try {
            $tree = NavigationMenu::getNavigationTree(auth()->user());

            return response()->json([
                'success' => true,
                'tree' => $tree,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to get navigation tree: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get navigation tree: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check if updating parent would create circular reference
     */
    private function wouldCreateCircularReference(int $itemId, ?int $newParentId): bool
    {
        if (!$newParentId || $newParentId === $itemId) {
            return $newParentId === $itemId;
        }

        $parent = NavigationMenu::find($newParentId);
        while ($parent) {
            if ($parent->id === $itemId) {
                return true;
            }
            $parent = $parent->parent;
        }

        return false;
    }

    /**
     * Generate default navigation structure from plugins
     */
    private function generateDefaultNavigation(): void
    {
        $pluginManager = app(\App\Services\PluginManager::class);
        $enabledPlugins = $pluginManager->getEnabledPlugins();

        $sortOrder = 1;

        // Create Dashboard menu item
        NavigationMenu::create([
            'name' => 'dashboard',
            'label' => 'Dashboard',
            'icon' => 'fas fa-tachometer-alt',
            'route' => 'dashboard',
            'permissions' => [], // No permissions required for dashboard
            'sort_order' => $sortOrder++,
            'is_system' => true,
            'is_active' => true,
            'visible' => true,
        ]);

        // Create detailed plugin-based menu items with sub-menus
        foreach ($enabledPlugins as $pluginName => $plugin) {
            $this->createPluginNavigation($pluginName, $plugin, $sortOrder);
        }

        // Create system navigation items
        $this->createSystemNavigation($sortOrder);
    }

    /**
     * Create navigation items for a specific plugin
     */
    private function createPluginNavigation(string $pluginName, $plugin, int &$sortOrder): void
    {
        switch ($pluginName) {
            case 'users':
                $this->createUsersNavigation($sortOrder);
                break;
            case 'business':
                $this->createBusinessNavigation($sortOrder);
                break;
            case 'announcements':
                $this->createAnnouncementsNavigation($sortOrder);
                break;
            case 'settings':
                $this->createSettingsNavigation($sortOrder);
                break;
            default:
                // For other plugins, use basic navigation structure
                $this->createBasicPluginNavigation($pluginName, $plugin, $sortOrder);
                break;
        }
    }

    /**
     * Create Users plugin navigation with sub-menus
     */
    private function createUsersNavigation(int &$sortOrder): void
    {
        $usersMenu = NavigationMenu::create([
            'name' => 'users',
            'label' => 'Users',
            'icon' => 'fas fa-users',
            'permissions' => ['manage_users', 'manage_roles', 'manage_permissions'],
            'sort_order' => $sortOrder++,
            'is_system' => false,
            'plugin' => 'users',
        ]);

        // Users submenu
        NavigationMenu::create([
            'name' => 'users-list',
            'label' => 'All Users',
            'icon' => 'fas fa-user',
            'route' => 'users.index',
            'parent_id' => $usersMenu->id,
            'permissions' => ['manage_users'],
            'sort_order' => 1,
            'is_system' => false,
            'plugin' => 'users',
        ]);

        NavigationMenu::create([
            'name' => 'roles-list',
            'label' => 'Roles',
            'icon' => 'fas fa-user-tag',
            'route' => 'roles.index',
            'parent_id' => $usersMenu->id,
            'permissions' => ['manage_roles'],
            'sort_order' => 2,
            'is_system' => false,
            'plugin' => 'users',
        ]);

        NavigationMenu::create([
            'name' => 'permissions-list',
            'label' => 'Permissions',
            'icon' => 'fas fa-key',
            'route' => 'permissions.index',
            'parent_id' => $usersMenu->id,
            'permissions' => ['manage_permissions'],
            'sort_order' => 3,
            'is_system' => false,
            'plugin' => 'users',
        ]);
    }

    /**
     * Create Business plugin navigation with sub-menus
     */
    private function createBusinessNavigation(int &$sortOrder): void
    {
        $businessMenu = NavigationMenu::create([
            'name' => 'business',
            'label' => 'Business',
            'icon' => 'fas fa-building',
            'permissions' => ['manage_businesses', 'view_businesses'],
            'sort_order' => $sortOrder++,
            'is_system' => false,
            'plugin' => 'business',
        ]);

        NavigationMenu::create([
            'name' => 'businesses-list',
            'label' => 'All Businesses',
            'icon' => 'fas fa-building',
            'route' => 'business.index',
            'parent_id' => $businessMenu->id,
            'permissions' => ['manage_businesses', 'view_businesses'],
            'sort_order' => 1,
            'is_system' => false,
            'plugin' => 'business',
        ]);

        NavigationMenu::create([
            'name' => 'contacts-list',
            'label' => 'Contacts',
            'icon' => 'fas fa-address-book',
            'route' => 'business.contacts.index',
            'parent_id' => $businessMenu->id,
            'permissions' => ['manage_contacts', 'view_contacts'],
            'sort_order' => 2,
            'is_system' => false,
            'plugin' => 'business',
        ]);
    }

    /**
     * Create Announcements plugin navigation
     */
    private function createAnnouncementsNavigation(int &$sortOrder): void
    {
        NavigationMenu::create([
            'name' => 'announcements',
            'label' => 'Announcements',
            'icon' => 'fas fa-bullhorn',
            'route' => 'announcements.index',
            'permissions' => ['manage_announcements', 'view_announcements'],
            'sort_order' => $sortOrder++,
            'is_system' => false,
            'plugin' => 'announcements',
        ]);
    }

    /**
     * Create Settings plugin navigation
     */
    private function createSettingsNavigation(int &$sortOrder): void
    {
        NavigationMenu::create([
            'name' => 'settings',
            'label' => 'Settings',
            'icon' => 'fas fa-cog',
            'route' => 'settings.index',
            'permissions' => ['manage_settings'],
            'sort_order' => $sortOrder++,
            'is_system' => false,
            'plugin' => 'settings',
        ]);
    }

    /**
     * Create basic navigation for other plugins
     */
    private function createBasicPluginNavigation(string $pluginName, $plugin, int &$sortOrder): void
    {
        if ($plugin->config['navigation'] ?? false) {
            $navigation = $plugin->config['navigation'];

            NavigationMenu::create([
                'name' => $pluginName,
                'label' => $navigation['label'] ?? ucfirst($pluginName),
                'icon' => $navigation['icon'] ?? 'fas fa-puzzle-piece',
                'route' => $navigation['route'] ?? null,
                'url' => $navigation['url'] ?? null,
                'plugin' => $pluginName,
                'permissions' => $navigation['permissions'] ?? [],
                'sort_order' => $sortOrder++,
                'is_system' => false,
            ]);
        }
    }

    /**
     * Create system navigation items
     */
    private function createSystemNavigation(int &$sortOrder): void
    {
        // Navigation Management (Admin only)
        NavigationMenu::create([
            'name' => 'navigation',
            'label' => 'Navigation',
            'icon' => 'fas fa-sitemap',
            'route' => 'navigation.index',
            'permissions' => ['manage_plugins'],
            'sort_order' => $sortOrder++,
            'is_system' => true,
        ]);

        // Plugin Management
        NavigationMenu::create([
            'name' => 'plugins',
            'label' => 'Plugins',
            'icon' => 'fas fa-puzzle-piece',
            'route' => 'plugins.index',
            'permissions' => ['manage_plugins'],
            'sort_order' => $sortOrder++,
            'is_system' => true,
        ]);
    }
}
